package sangfordata

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/signal"
	"regexp"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/firmware"
	"github.com/flipped-aurora/gin-vue-admin/server/model/sangforapi"
	"github.com/panjf2000/gnet/v2"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
	"gorm.io/gorm"
)

// FirmwareEventQueue 固件事件队列管理
type FirmwareEventQueue struct {
	events    []sangforapi.SecurityEvent
	mutex     sync.Mutex
	batchSize int
	flushChan chan struct{}
}

// FirmwareInfoQueue 固件信息队列管理
type FirmwareInfoQueue struct {
	infos     []firmware.Firmware
	mutex     sync.Mutex
	batchSize int
	flush<PERSON>han chan struct{}
}

// FirmwareChunk 固件数据分片结构（与发送端保持一致）
type FirmwareChunk struct {
	ChunkID     string `json:"chunk_id"`     // 分片唯一标识
	ChunkIndex  int    `json:"chunk_index"`  // 当前分片索引（从0开始）
	TotalChunks int    `json:"total_chunks"` // 总分片数
	FirmwareID  uint   `json:"firmware_id"`  // 固件ID
	Data        string `json:"data"`         // 分片数据（Base64编码）
	Timestamp   int64  `json:"timestamp"`    // 时间戳
}

// ChunkAssembler 分片组装器
type ChunkAssembler struct {
	chunks    map[string]map[int]FirmwareChunk // chunkID -> chunkIndex -> chunk
	mutex     sync.RWMutex
	timeout   time.Duration
	cleanupCh chan string
}

// NewChunkAssembler 创建分片组装器
func NewChunkAssembler() *ChunkAssembler {
	// 从配置文件读取超时设置
	timeoutMinutes := global.GVA_CONFIG.FirmwareAnalysis.ChunkTransfer.ChunkTimeout
	assembler := &ChunkAssembler{
		chunks:    make(map[string]map[int]FirmwareChunk),
		timeout:   time.Duration(timeoutMinutes) * time.Minute,
		cleanupCh: make(chan string, 100),
	}

	// 启动清理协程
	go assembler.startCleanupWorker()

	return assembler
}

// AddChunk 添加分片到组装器
func (ca *ChunkAssembler) AddChunk(chunk FirmwareChunk) (*firmware.Firmware, error) {
	ca.mutex.Lock()
	defer ca.mutex.Unlock()

	// 初始化chunkID对应的map
	if ca.chunks[chunk.ChunkID] == nil {
		ca.chunks[chunk.ChunkID] = make(map[int]FirmwareChunk)
	}

	// 添加分片
	ca.chunks[chunk.ChunkID][chunk.ChunkIndex] = chunk

	// 检查是否收集齐所有分片
	chunkMap := ca.chunks[chunk.ChunkID]
	if len(chunkMap) == chunk.TotalChunks {
		// 所有分片已收集齐，开始组装
		global.GVA_LOG.Info("开始组装固件分片",
			zap.String("chunkID", chunk.ChunkID),
			zap.Int("totalChunks", chunk.TotalChunks))

		// 按索引顺序组装数据
		var assembledData []byte
		for i := 0; i < chunk.TotalChunks; i++ {
			if chunkData, exists := chunkMap[i]; exists {
				// 解码Base64数据
				decodedData, err := base64.StdEncoding.DecodeString(chunkData.Data)
				if err != nil {
					global.GVA_LOG.Error("解码分片数据失败",
						zap.Error(err),
						zap.String("chunkID", chunk.ChunkID),
						zap.Int("chunkIndex", i))
					return nil, fmt.Errorf("failed to decode chunk %d: %v", i, err)
				}
				assembledData = append(assembledData, decodedData...)
			} else {
				return nil, fmt.Errorf("missing chunk %d for chunkID %s", i, chunk.ChunkID)
			}
		}

		// 反序列化为Firmware对象
		var firmwareInfo firmware.Firmware
		if err := json.Unmarshal(assembledData, &firmwareInfo); err != nil {
			global.GVA_LOG.Error("反序列化组装后的固件数据失败",
				zap.Error(err),
				zap.String("chunkID", chunk.ChunkID))
			return nil, fmt.Errorf("failed to unmarshal assembled data: %v", err)
		}

		// 清理已组装的分片
		delete(ca.chunks, chunk.ChunkID)

		global.GVA_LOG.Info("固件分片组装完成",
			zap.String("chunkID", chunk.ChunkID),
			zap.Uint("firmwareID", firmwareInfo.ID),
			zap.Int("assembledSize", len(assembledData)))

		return &firmwareInfo, nil
	}

	// 还未收集齐所有分片 - 只在关键节点记录日志
	if len(chunkMap)%10 == 0 || len(chunkMap) == chunk.TotalChunks-1 {
		global.GVA_LOG.Debug("分片收集进度",
			zap.String("chunkID", chunk.ChunkID),
			zap.Int("collected", len(chunkMap)),
			zap.Int("total", chunk.TotalChunks))
	}

	return nil, nil
}

// startCleanupWorker 启动清理工作协程
func (ca *ChunkAssembler) startCleanupWorker() {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ca.cleanupExpiredChunks()
		case chunkID := <-ca.cleanupCh:
			ca.cleanupSpecificChunk(chunkID)
		}
	}
}

// cleanupExpiredChunks 清理过期的分片
func (ca *ChunkAssembler) cleanupExpiredChunks() {
	ca.mutex.Lock()
	defer ca.mutex.Unlock()

	now := time.Now().Unix()
	cleanedCount := 0
	for chunkID, chunkMap := range ca.chunks {
		// 检查第一个分片的时间戳
		for _, chunk := range chunkMap {
			if now-chunk.Timestamp > int64(ca.timeout.Seconds()) {
				delete(ca.chunks, chunkID)
				cleanedCount++
			}
			break // 只检查第一个分片的时间戳
		}
	}

	// 只在有清理动作时记录日志
	if cleanedCount > 0 {
		global.GVA_LOG.Warn("清理过期分片", zap.Int("cleanedCount", cleanedCount))
	}
}

// cleanupSpecificChunk 清理指定的分片
func (ca *ChunkAssembler) cleanupSpecificChunk(chunkID string) {
	ca.mutex.Lock()
	defer ca.mutex.Unlock()

	if _, exists := ca.chunks[chunkID]; exists {
		delete(ca.chunks, chunkID)
		global.GVA_LOG.Debug("清理指定分片", zap.String("chunkID", chunkID))
	}
}

// FirmwareUDPServer 固件数据专用UDP服务器
type FirmwareUDPServer struct {
	gnet.BuiltinEventEngine
	addr           string
	running        bool
	mu             sync.Mutex
	wg             sync.WaitGroup
	logger         *log.Logger
	bufferPool     sync.Pool
	limiter        *rate.Limiter
	maxWorkers     int
	workerTokens   chan struct{}
	metrics        FirmwareMetrics
	timeout        time.Duration
	messageQueue   *firmwareMessageQueue
	processTimeout time.Duration
	eventQueue     *FirmwareEventQueue
	infoQueue      *FirmwareInfoQueue // 新增固件信息队列
	chunkAssembler *ChunkAssembler    // 新增分片组装器
}

// FirmwareMetrics 固件服务器指标
type FirmwareMetrics struct {
	totalRequests     uint64
	successRequests   uint64
	failedRequests    uint64
	droppedRequests   uint64
	processingTime    uint64
	currentGoroutines int64
	eventsProcessed   uint64 // 处理的事件数量
	infoProcessed     uint64 // 处理的固件信息数量
	dbWriteErrors     uint64 // 数据库写入错误数
}

// firmwareMessageQueue 固件消息队列
type firmwareMessageQueue struct {
	queue     chan []byte
	capacity  int
	batchSize int
}

// NewFirmwareEventQueue 创建固件事件队列
func NewFirmwareEventQueue(batchSize int) *FirmwareEventQueue {
	queue := &FirmwareEventQueue{
		events:    make([]sangforapi.SecurityEvent, 0, batchSize*2),
		batchSize: batchSize,
		flushChan: make(chan struct{}, 1),
	}

	// 启动后台刷新协程
	go queue.startFlushWorker()

	return queue
}

// NewFirmwareInfoQueue 创建固件信息队列
func NewFirmwareInfoQueue(batchSize int) *FirmwareInfoQueue {
	queue := &FirmwareInfoQueue{
		infos:     make([]firmware.Firmware, 0, batchSize*2),
		batchSize: batchSize,
		flushChan: make(chan struct{}, 1),
	}

	// 启动后台刷新协程
	go queue.startFlushWorker()

	return queue
}

// AddEvent 添加事件到队列
func (feq *FirmwareEventQueue) AddEvent(event sangforapi.SecurityEvent) {
	feq.mutex.Lock()
	feq.events = append(feq.events, event)
	currentSize := len(feq.events)
	feq.mutex.Unlock()

	// 如果达到批次大小，触发刷新
	if currentSize >= feq.batchSize {
		select {
		case feq.flushChan <- struct{}{}:
			// 触发成功
		default:
			// 已经有刷新任务在进行
		}
	}
}

// AddInfo 添加固件信息到队列
func (fiq *FirmwareInfoQueue) AddInfo(info firmware.Firmware) {
	fiq.mutex.Lock()
	fiq.infos = append(fiq.infos, info)
	currentSize := len(fiq.infos)
	fiq.mutex.Unlock()

	// 如果达到批次大小，触发刷新
	if currentSize >= fiq.batchSize {
		select {
		case fiq.flushChan <- struct{}{}:
			// 触发成功
		default:
			// 已经有刷新任务在进行
		}
	}
}

// startFlushWorker 启动刷新工作协程
func (feq *FirmwareEventQueue) startFlushWorker() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒强制刷新一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			feq.flushToDB()
		case <-feq.flushChan:
			feq.flushToDB()
		}
	}
}

// startFlushWorker 启动固件信息刷新工作协程
func (fiq *FirmwareInfoQueue) startFlushWorker() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒强制刷新一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			fiq.flushToDB()
		case <-fiq.flushChan:
			fiq.flushToDB()
		}
	}
}

// flushToDB 将事件批量写入数据库
func (feq *FirmwareEventQueue) flushToDB() {
	feq.mutex.Lock()
	if len(feq.events) == 0 {
		feq.mutex.Unlock()
		return
	}

	// 获取当前队列内容
	eventsToWrite := make([]sangforapi.SecurityEvent, len(feq.events))
	copy(eventsToWrite, feq.events)
	feq.events = feq.events[:0] // 清空队列但保留容量
	feq.mutex.Unlock()

	// 使用事务批量写入数据库
	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 分批插入，每批50条
		batchSize := 50
		for i := 0; i < len(eventsToWrite); i += batchSize {
			end := i + batchSize
			if end > len(eventsToWrite) {
				end = len(eventsToWrite)
			}

			if err := tx.Create(eventsToWrite[i:end]).Error; err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		global.GVA_LOG.Error("批量写入固件安全事件失败",
			zap.Error(err),
			zap.Int("eventCount", len(eventsToWrite)))

		// 写入失败时，尝试将数据放回队列（简单的重试机制）
		feq.mutex.Lock()
		if len(feq.events)+len(eventsToWrite) <= cap(feq.events) {
			feq.events = append(feq.events, eventsToWrite...)
		} else {
			global.GVA_LOG.Error("队列已满，丢弃部分固件事件数据",
				zap.Int("dropped", len(eventsToWrite)))
		}
		feq.mutex.Unlock()
	} else {
		global.GVA_LOG.Info("成功批量写入固件安全事件",
			zap.Int("eventCount", len(eventsToWrite)))
	}
}

// flushToDB 将固件信息批量写入数据库
func (fiq *FirmwareInfoQueue) flushToDB() {
	fiq.mutex.Lock()
	if len(fiq.infos) == 0 {
		fiq.mutex.Unlock()
		return
	}

	// 获取当前队列内容
	infosToWrite := make([]firmware.Firmware, len(fiq.infos))
	copy(infosToWrite, fiq.infos)
	fiq.infos = fiq.infos[:0] // 清空队列但保留容量
	fiq.mutex.Unlock()

	// 使用事务批量写入数据库
	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 分批插入，每批10条(固件信息可能较大)
		batchSize := 10
		for i := 0; i < len(infosToWrite); i += batchSize {
			end := i + batchSize
			if end > len(infosToWrite) {
				end = len(infosToWrite)
			}

			// 对每条固件信息进行处理
			for j := i; j < end; j++ {
				info := infosToWrite[j]

				// 检查是否已存在相同ID的记录
				var existingCount int64
				if err := tx.Model(&firmware.Firmware{}).Where("id = ?", info.ID).Count(&existingCount).Error; err != nil {
					return err
				}

				if existingCount > 0 {
					// 如果记录已存在，则更新
					if err := tx.Model(&firmware.Firmware{}).Where("id = ?", info.ID).Updates(info).Error; err != nil {
						return err
					}
				} else {
					// 如果记录不存在，则创建新记录
					if err := tx.Create(&info).Error; err != nil {
						return err
					}
				}
			}
		}
		return nil
	})

	if err != nil {
		global.GVA_LOG.Error("批量写入固件信息失败",
			zap.Error(err),
			zap.Int("infoCount", len(infosToWrite)))

		// 写入失败时，尝试将数据放回队列（简单的重试机制）
		fiq.mutex.Lock()
		if len(fiq.infos)+len(infosToWrite) <= cap(fiq.infos) {
			fiq.infos = append(fiq.infos, infosToWrite...)
		} else {
			global.GVA_LOG.Error("队列已满，丢弃部分固件信息数据",
				zap.Int("dropped", len(infosToWrite)))
		}
		fiq.mutex.Unlock()
	} else {
		global.GVA_LOG.Info("成功批量写入固件信息",
			zap.Int("infoCount", len(infosToWrite)))
	}
}

// newFirmwareMessageQueue 创建固件消息队列
func newFirmwareMessageQueue(capacity int, batchSize int) *firmwareMessageQueue {
	return &firmwareMessageQueue{
		queue:     make(chan []byte, capacity),
		capacity:  capacity,
		batchSize: batchSize,
	}
}

// cleanSyslogMessage 清理syslog消息格式
func cleanSyslogMessage(message string, programName string) string {
	message = strings.TrimSpace(message)

	// 定位 "programName:" 的结束位置
	// 需要确保 programName 不是空字符串，否则 programNameMarker 会是 ":" 可能会匹配错误
	if programName != "" {
		programNameMarker := programName + ":"
		markerIndex := strings.Index(message, programNameMarker)

		if markerIndex != -1 {
			// 从 programNameMarker 之后开始查找 '{' 或 '['
			jsonSearchStart := markerIndex + len(programNameMarker)
			if jsonSearchStart < len(message) {
				remainingMessage := message[jsonSearchStart:]
				// Trim any leading spaces or newlines before JSON starts
				trimmedRemainingMessage := strings.TrimSpace(remainingMessage)

				jsonStartIndexInTrimmed := -1
				if len(trimmedRemainingMessage) > 0 {
					if trimmedRemainingMessage[0] == '{' {
						jsonStartIndexInTrimmed = 0
					} else if trimmedRemainingMessage[0] == '[' {
						jsonStartIndexInTrimmed = 0
					}
				}

				if jsonStartIndexInTrimmed != -1 {
					// We return the trimmed version from the actual JSON start
					return trimmedRemainingMessage[jsonStartIndexInTrimmed:]
				}
			}
			global.GVA_LOG.Debug("JSON起始符在programName标记后未找到", zap.String("programName", programName))
		} else {
			global.GVA_LOG.Debug("programName标记在消息中未找到", zap.String("programName", programName))
		}
	} else {
		global.GVA_LOG.Debug("接收到空的programName参数")
	}

	// 如果上面的方法失败了，回退到旧的简单查找逻辑（作为保险）
	global.GVA_LOG.Debug("回退到简单JSON查找逻辑", zap.String("programName", programName))

	// 旧逻辑，尽量保证向前兼容和处理未知情况
	// 移除syslog头部信息，提取JSON数据 (旧逻辑，可能不太精确)
	// 再次 TrimSpace 以防万一
	tempMessage := strings.TrimSpace(message)
	jsonStart := -1
	if strings.HasPrefix(tempMessage, "{") || strings.HasPrefix(tempMessage, "[") {
		jsonStart = 0 // JSON就在开头
	} else {
		// 尝试从冒号后查找
		colonIndex := strings.Index(tempMessage, ":")
		if colonIndex != -1 && colonIndex+1 < len(tempMessage) {
			potentialJson := strings.TrimSpace(tempMessage[colonIndex+1:])
			if strings.HasPrefix(potentialJson, "{") || strings.HasPrefix(potentialJson, "[") {
				return potentialJson
			}
		}
		// 如果还是找不到，执行最原始的查找
		jsonStart = strings.Index(tempMessage, "[")
		if jsonStart == -1 {
			jsonStart = strings.Index(tempMessage, "{")
		}
	}

	if jsonStart != -1 {
		return tempMessage[jsonStart:]
	}

	global.GVA_LOG.Error("无法定位JSON内容", zap.String("programName", programName))
	return tempMessage // 最坏情况，返回原始裁剪过的message
}

// extractProgramName 从syslog消息中提取程序名
func extractProgramName(message string) string {
	// 移除过于详细的调试日志
	// 使用正则表达式匹配syslog格式中的程序名
	// 格式: <优先级>时间戳 主机名 程序名: JSON数据
	re := regexp.MustCompile(`<\d+>[^ ]+ [^ ]+ ([^:]+):`)
	matches := re.FindStringSubmatch(message)
	if len(matches) >= 2 {
		return strings.TrimSpace(matches[1])
	}

	// 兼容性处理：尝试用空格分割并查找冒号
	parts := strings.Split(message, " ")
	for i, part := range parts {
		if strings.HasSuffix(part, ":") && i > 2 {
			return strings.TrimSuffix(part, ":")
		}
	}

	// 再次尝试匹配特定的关键字
	if strings.Contains(message, "security-events") {
		return "security-events"
	}
	if strings.Contains(message, "firmware-info") {
		return "firmware-info"
	}

	return ""
}

// handleFirmwareSyslogMessage 处理固件syslog消息
func (fus *FirmwareUDPServer) handleFirmwareSyslogMessage(data []byte) error {
	if len(data) == 0 {
		return fmt.Errorf("empty message content")
	}

	message := string(data)

	programName := extractProgramName(message)

	// 使用 programName 来辅助清理消息
	cleanedMessage := cleanSyslogMessage(message, programName)

	// 只在处理分片时记录详细日志
	if programName == "firmware-chunk" {
		global.GVA_LOG.Debug("接收到分片消息",
			zap.String("programName", programName),
			zap.Int("messageLength", len(cleanedMessage)))
	}

	switch programName {
	case "security-events":
		// 处理安全事件
		return fus.handleSecurityEvents(cleanedMessage)
	case "firmware-info":
		// 处理固件信息（完整数据）
		return fus.handleFirmwareInfo(cleanedMessage)
	case "firmware-chunk":
		// 处理固件信息分片
		return fus.handleFirmwareChunk(cleanedMessage)
	default:
		// 未知类型的消息
		global.GVA_LOG.Warn("接收到未知类型的syslog消息",
			zap.String("programName", programName))
		return fmt.Errorf("unknown program name: %s", programName)
	}
}

// handleSecurityEvents 处理安全事件消息
func (fus *FirmwareUDPServer) handleSecurityEvents(message string) error {
	// 解析JSON数据
	var events []sangforapi.SecurityEvent
	if err := json.Unmarshal([]byte(message), &events); err != nil {
		global.GVA_LOG.Error("解析安全事件JSON失败",
			zap.Error(err),
			zap.String("message", message[:min(200, len(message))]))
		return fmt.Errorf("failed to parse security events JSON: %v", err)
	}

	// 将事件添加到队列
	for _, event := range events {
		fus.eventQueue.AddEvent(event)
		atomic.AddUint64(&fus.metrics.eventsProcessed, 1)
	}

	global.GVA_LOG.Info("成功处理安全事件",
		zap.Int("eventCount", len(events)))

	return nil
}

// handleFirmwareInfo 处理固件信息消息
func (fus *FirmwareUDPServer) handleFirmwareInfo(message string) error {
	// 解析JSON数据
	var firmwareInfo firmware.Firmware
	if err := json.Unmarshal([]byte(message), &firmwareInfo); err != nil {
		global.GVA_LOG.Error("解析固件信息JSON失败",
			zap.Error(err),
			zap.String("message", message[:min(200, len(message))]))
		return fmt.Errorf("failed to parse firmware info JSON: %v", err)
	}

	// 处理Result字段过大的问题
	if len(firmwareInfo.Result) > 65000 {
		global.GVA_LOG.Warn("固件Result字段过大，将进行压缩处理",
			zap.Uint("firmwareID", firmwareInfo.ID),
			zap.Int("resultSize", len(firmwareInfo.Result)))

		// 创建一个压缩后的Result字段的副本
		var resultObj interface{}
		if err := json.Unmarshal(firmwareInfo.Result, &resultObj); err != nil {
			global.GVA_LOG.Error("解析Result字段失败", zap.Error(err))
		} else {
			// 将结果压缩为摘要信息
			summary := map[string]interface{}{
				"_compressed": true,
				"summary":     "Result字段过大已压缩，请查看原系统获取完整数据",
			}
			if compressedJSON, err := json.Marshal(summary); err == nil {
				firmwareInfo.Result = compressedJSON
			}
		}
	}

	// 将固件信息添加到队列
	fus.infoQueue.AddInfo(firmwareInfo)
	atomic.AddUint64(&fus.metrics.infoProcessed, 1)

	global.GVA_LOG.Info("成功处理固件信息",
		zap.Uint("firmwareID", firmwareInfo.ID),
		zap.String("name", firmwareInfo.Name),
		zap.Int("resultSize", len(firmwareInfo.Result)))

	return nil
}

// handleFirmwareChunk 处理固件信息分片消息
func (fus *FirmwareUDPServer) handleFirmwareChunk(message string) error {
	// 解析JSON数据
	var chunk FirmwareChunk
	if err := json.Unmarshal([]byte(message), &chunk); err != nil {
		global.GVA_LOG.Error("解析固件分片JSON失败",
			zap.Error(err),
			zap.String("message", message[:min(200, len(message))]))
		return fmt.Errorf("failed to parse firmware chunk JSON: %v", err)
	}

	// 只在关键节点记录分片接收日志（每10个分片或第一个/最后一个）
	if chunk.ChunkIndex%10 == 0 || chunk.ChunkIndex == 0 || chunk.ChunkIndex == chunk.TotalChunks-1 {
		global.GVA_LOG.Debug("接收固件分片",
			zap.String("chunkID", chunk.ChunkID),
			zap.Int("chunkIndex", chunk.ChunkIndex),
			zap.Int("totalChunks", chunk.TotalChunks),
			zap.Uint("firmwareID", chunk.FirmwareID))
	}

	// 将分片添加到组装器
	assembledFirmware, err := fus.chunkAssembler.AddChunk(chunk)
	if err != nil {
		global.GVA_LOG.Error("添加分片到组装器失败",
			zap.Error(err),
			zap.String("chunkID", chunk.ChunkID),
			zap.Int("chunkIndex", chunk.ChunkIndex))
		return fmt.Errorf("failed to add chunk to assembler: %v", err)
	}

	// 如果组装完成，处理完整的固件信息
	if assembledFirmware != nil {
		global.GVA_LOG.Info("固件分片组装并处理完成",
			zap.String("chunkID", chunk.ChunkID),
			zap.Uint("firmwareID", assembledFirmware.ID),
			zap.String("name", assembledFirmware.Name),
			zap.Int("resultSize", len(assembledFirmware.Result)))

		// 将组装好的固件信息添加到队列
		fus.infoQueue.AddInfo(*assembledFirmware)
		atomic.AddUint64(&fus.metrics.infoProcessed, 1)
	}

	return nil
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// OnTraffic 处理UDP流量
func (fus *FirmwareUDPServer) OnTraffic(c gnet.Conn) (action gnet.Action) {
	buf, err := c.Next(-1)
	if err != nil {
		fus.logger.Printf("读取UDP数据错误: %v", err)
		atomic.AddUint64(&fus.metrics.failedRequests, 1)
		return gnet.None
	}

	atomic.AddUint64(&fus.metrics.totalRequests, 1)

	if len(buf) == 0 {
		return gnet.None
	}

	// 只在处理分片或大数据包时记录详细信息
	message := string(buf)
	programName := extractProgramName(message)

	// 只记录关键信息，减少日志量
	if len(buf) > 10000 || programName == "firmware-chunk" {
		fus.logger.Printf("收到数据包: %d bytes from %s, type: %s", len(buf), c.RemoteAddr(), programName)
	}

	// 尝试将消息放入队列
	select {
	case fus.messageQueue.queue <- buf:
		// 成功放入队列
	default:
		// 队列已满，增加丢弃计数
		atomic.AddUint64(&fus.metrics.droppedRequests, 1)
		fus.logger.Printf("消息队列已满,丢弃数据包: %d bytes", len(buf))
	}

	return gnet.None
}

// OnTick 定期状态报告
func (fus *FirmwareUDPServer) OnTick() (delay time.Duration, action gnet.Action) {
	metrics := fus.getMetrics()

	fus.logger.Printf("服务器状态 - 总请求:%d 成功:%d 失败:%d 丢弃:%d 处理事件:%d 处理固件:%d 协程:%d 队列:%d/%d",
		metrics.totalRequests,
		metrics.successRequests,
		metrics.failedRequests,
		metrics.droppedRequests,
		metrics.eventsProcessed,
		metrics.infoProcessed,
		metrics.currentGoroutines,
		len(fus.messageQueue.queue),
		fus.messageQueue.capacity)

	return 30 * time.Second, gnet.None
}

// getMetrics 获取服务器指标
func (fus *FirmwareUDPServer) getMetrics() FirmwareMetrics {
	return FirmwareMetrics{
		totalRequests:     atomic.LoadUint64(&fus.metrics.totalRequests),
		successRequests:   atomic.LoadUint64(&fus.metrics.successRequests),
		failedRequests:    atomic.LoadUint64(&fus.metrics.failedRequests),
		droppedRequests:   atomic.LoadUint64(&fus.metrics.droppedRequests),
		processingTime:    atomic.LoadUint64(&fus.metrics.processingTime),
		currentGoroutines: atomic.LoadInt64(&fus.metrics.currentGoroutines),
		eventsProcessed:   atomic.LoadUint64(&fus.metrics.eventsProcessed),
		infoProcessed:     atomic.LoadUint64(&fus.metrics.infoProcessed),
		dbWriteErrors:     atomic.LoadUint64(&fus.metrics.dbWriteErrors),
	}
}

// NewFirmwareUDPServer 创建固件UDP服务器
func NewFirmwareUDPServer(addr string) *FirmwareUDPServer {
	logger := log.New(os.Stderr, "[UDP Server] ", log.LstdFlags|log.Lshortfile)

	server := &FirmwareUDPServer{
		addr:           addr,
		logger:         logger,
		maxWorkers:     200,              // 固件数据处理不需要太多并发
		timeout:        10 * time.Second, // 较长的超时时间，确保可靠性
		processTimeout: 15 * time.Second,
		bufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 65536) // 增加到64KB缓冲区，处理更大的数据包
			},
		},
		limiter:        rate.NewLimiter(rate.Limit(1000), 500), // 较低的限流，重视可靠性
		messageQueue:   newFirmwareMessageQueue(5000, 100),     // 较小的队列，避免内存占用过大
		eventQueue:     NewFirmwareEventQueue(100),             // 批次大小100
		infoQueue:      NewFirmwareInfoQueue(20),               // 固件信息批次大小20
		chunkAssembler: NewChunkAssembler(),                    // 初始化分片组装器
	}

	server.workerTokens = make(chan struct{}, server.maxWorkers)
	return server
}

// startWorkerPool 启动工作池
func (fus *FirmwareUDPServer) startWorkerPool(ctx context.Context) {
	for i := 0; i < fus.maxWorkers; i++ {
		go fus.worker(ctx)
	}
}

// worker 单个工作协程
func (fus *FirmwareUDPServer) worker(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case data := <-fus.messageQueue.queue:
			fus.processMessage(ctx, data)
		}
	}
}

// processMessage 处理单条消息
func (fus *FirmwareUDPServer) processMessage(ctx context.Context, data []byte) {
	start := time.Now()

	// 创建带超时的上下文
	processCtx, cancel := context.WithTimeout(ctx, fus.processTimeout)
	defer cancel()

	// 检查上下文是否已取消
	select {
	case <-processCtx.Done():
		atomic.AddUint64(&fus.metrics.failedRequests, 1)
		return
	default:
	}

	// 处理消息，重试机制
	var err error
	for i := 0; i < 3; i++ {
		err = fus.handleFirmwareSyslogMessage(data)
		if err == nil {
			break
		}

		// 短暂等待后重试
		select {
		case <-processCtx.Done():
			atomic.AddUint64(&fus.metrics.failedRequests, 1)
			return
		case <-time.After(time.Millisecond * 200):
		}
	}

	processingTime := time.Since(start)

	if err != nil {
		atomic.AddUint64(&fus.metrics.failedRequests, 1)
		global.GVA_LOG.Error("处理消息失败",
			zap.Error(err),
			zap.Duration("耗时", processingTime))
	} else {
		atomic.AddUint64(&fus.metrics.successRequests, 1)
		if processingTime > time.Second {
			fus.logger.Printf("消息处理耗时较长: %v", processingTime)
		}
	}
}

// Run 启动固件UDP服务器
func (fus *FirmwareUDPServer) Run() error {
	fus.mu.Lock()
	if fus.running {
		fus.mu.Unlock()
		return fmt.Errorf("服务器已在运行")
	}
	fus.running = true
	fus.mu.Unlock()

	// 设置信号处理
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 捕获系统信号
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigCh
		fus.logger.Println("收到关闭信号，正在优雅关闭服务器...")
		cancel()
	}()

	// 启动工作池
	fus.startWorkerPool(ctx)

	// 启动服务器
	for {
		select {
		case <-ctx.Done():
			fus.logger.Println("正在等待所有处理任务完成...")
			fus.wg.Wait()
			fus.logger.Println("UDP服务器已安全关闭")
			return nil
		default:
			fus.logger.Printf("UDP服务器正在启动，监听地址: %s", fus.addr)
			err := gnet.Run(fus, fus.addr,
				gnet.WithMulticore(true),
				gnet.WithReusePort(true),
				gnet.WithTicker(true),
			)

			if err != nil {
				fus.logger.Printf("服务器运行出错: %v，5秒后重试...", err)
				time.Sleep(5 * time.Second)
				continue
			}
		}
	}
}

// SysLogRecFirmware 启动固件syslog接收服务
func SysLogRecFirmware() {
	// 创建固件UDP服务器
	server := NewFirmwareUDPServer("udp://0.0.0.0:5141")

	global.GVA_LOG.Info("启动syslog接收服务", zap.String("port", "5141"))

	if err := server.Run(); err != nil {
		global.GVA_LOG.Error("服务器启动失败", zap.Error(err))
	}
}
