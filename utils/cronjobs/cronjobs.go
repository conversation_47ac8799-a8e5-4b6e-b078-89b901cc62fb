package cronjobs

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service/attackanalysis"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/sangfordata"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/timer"
	"go.uber.org/zap"
)

func MonitorHoneypotTask() {
	// 定时任务，监控蜜罐运行状态，默认两分钟检查一次
	t := timer.NewTimerTask()
	_, err := t.AddTaskByFunc("monitor_honeypot_status", "@every 2m", func() {
		err := service.ServiceGroupApp.HoneypotServiceGroup.HoleService.MonitorHole()
		if err != nil {
			global.GVA_LOG.Error("蜜罐监控任务执行失败!", zap.Error(err))
		}
		global.GVA_LOG.Info("蜜罐监控任务执行成功，持续监控蜜罐健康状态中!")
	})
	if err != nil {
		global.GVA_LOG.Error("蜜罐监控任务创建失败!", zap.Error(err))
	}
	global.GVA_LOG.Info("蜜罐监控任务创建成功!")
}

func MonitorEsTask() {
	t := timer.NewTimerTask()
	_, err := t.AddTaskByFunc("monitor_elastic", "@every 1m", func() {
		err := service.ServiceGroupApp.AttackFlowServiceGroup.AttackFlowService.GetElasticAttackFlow()
		if err != nil {
			global.GVA_LOG.Error("elastic监控任务执行失败!", zap.Error(err))
		}
		global.GVA_LOG.Info("elastic监控任务执行成功，持续监控elastic中!")
	})
	if err != nil {
		global.GVA_LOG.Error("elastic监控任务创建失败!", zap.Error(err))
	}
	global.GVA_LOG.Info("elastic监控任务创建成功!")
}

// 定时监控探针状态
func MonitorProbeTask() {
	t := timer.NewTimerTask()
	_, err := t.AddTaskByFunc("monitor_elastic", "@every 1m", func() {
		err := service.ServiceGroupApp.AttackFlowServiceGroup.AttackFlowService.ProbeConnectStatus()
		if err != nil {
			global.GVA_LOG.Error("elastic监控任务执行失败!", zap.Error(err))
		}
		global.GVA_LOG.Info("elastic监控任务执行成功，持续监控elastic中!")
	})
	if err != nil {
		global.GVA_LOG.Error("elastic监控任务创建失败!", zap.Error(err))
	}
	global.GVA_LOG.Info("elastic监控任务创建成功!")
}

func MonitorHoneypotGroupTask() {
	// 定时任务，监控蜜罐组运行状态，默认三分钟检查一次
	t := timer.NewTimerTask()
	_, err := t.AddTaskByFunc("monitor_honeypotGroup_status", "@every 3m", func() {
		err := service.ServiceGroupApp.HoneypotServiceGroup.HolesGroupService.MonitorHoleGroup()
		if err != nil {
			global.GVA_LOG.Error("蜜罐组监控任务执行失败!", zap.Error(err))
		}
		global.GVA_LOG.Info("蜜罐组监控任务执行成功，持续监控蜜罐组健康状态中!")
	})
	if err != nil {
		global.GVA_LOG.Error("蜜罐组监控任务创建失败!", zap.Error(err))
	}
	global.GVA_LOG.Info("蜜罐组监控任务创建成功!")
}

// CleanHistoryDataTask 历史数据清理任务
func CleanHistoryDataTask() {
	t := timer.NewTimerTask()
	_, err := t.AddTaskByFunc("clean_history_data", "@daily", func() {
		global.GVA_LOG.Info("开始执行历史数据清理任务")
		sangfordata.RunCleanHistoryData()
		global.GVA_LOG.Info("历史数据清理任务执行完成")
	})
	if err != nil {
		global.GVA_LOG.Error("历史数据清理任务创建失败!", zap.Error(err))
	}
	global.GVA_LOG.Info("历史数据清理任务创建成功!")
}

func SubscribeAttackVideo() {
	var attackVideoService attackanalysis.AttackVideoService
	pubsub := global.GVA_REDIS.Subscribe(context.Background(), "attackVideo")
	for {
		msg, err := pubsub.ReceiveMessage(context.Background())
		if err != nil {
			global.GVA_LOG.Error("订阅攻击录像频道失败!", zap.Error(err))
		}
		filename := filepath.Base(msg.Payload)
		err = attackVideoService.CreateAttackVideoAuto(filename)
		if err != nil {
			global.GVA_LOG.Error("创建攻击直播记录失败!", zap.Error(err))
			return
		}
		global.GVA_LOG.Info(filename + " 新的攻击直播产生！")
		// 后台监控这次攻击行为，直到结束标识符的出现
		go func() {
			prevLength := int64(0)
			prevTimestamp := time.Now().Unix()
			finishSign := false
			for {
				// 第一个结束判定，判定是否超过超时时间
				if time.Now().Unix()-prevTimestamp >= global.GVA_CONFIG.Honeynet.VideoOverTime {
					global.GVA_LOG.Info("已超时，判定攻击结束")
					err := attackVideoService.FinishAttackVideo(filename)
					if err != nil {
						global.GVA_LOG.Error(err.Error())
						return
					}
					break
				}
				length, err := global.GVA_REDIS.ZCard(context.Background(), filename).Result()
				if err != nil {
					global.GVA_LOG.Error(err.Error())
					return
				}
				if length > prevLength {
					newData, err := global.GVA_REDIS.ZRange(context.Background(), filename, prevLength, length-1).Result()
					if err != nil {
						global.GVA_LOG.Error(err.Error())
						return
					}
					for _, x := range newData {
						// 第二个结束判定，是否出现了<END>字符
						if x == "<END>" {
							err := attackVideoService.FinishAttackVideo(filename)
							if err != nil {
								global.GVA_LOG.Error(err.Error())
								return
							}
							finishSign = true
						}
					}
					prevLength = length
					prevTimestamp = time.Now().Unix()
				}
				if finishSign {
					break
				}
				// 设置1/5秒的时间间隔，可以显著降低cpu使用率
				time.Sleep(time.Second / 5)
			}
			global.GVA_LOG.Info(filename + " 攻击结束！")
		}()
	}
}

func SubscribeWriteAttackVideoFinish() {
	pubsub := global.GVA_REDIS.Subscribe(context.Background(), "writeAttackVideoFinish")
	for {
		msg, err := pubsub.ReceiveMessage(context.Background())
		if err != nil {
			global.GVA_LOG.Error("订阅攻击录像频道失败!", zap.Error(err))
			continue
		}
		filename := filepath.Base(msg.Payload)
		data, err := global.GVA_REDIS.Get(context.Background(), filename).Result()
		if err != nil {
			global.GVA_LOG.Error(err.Error())
			continue
		}
		newFilename := strings.Split(filename, "file_")
		err = os.WriteFile(global.GVA_CONFIG.Honeynet.VideoPath+"/"+newFilename[1], []byte(data), 0644)
		if err != nil {
			global.GVA_LOG.Error("Error writing file:" + err.Error())
			continue
		}
		global.GVA_LOG.Info(filename + " 写入录像文件完成！")
		// 录像文件写入本地后，释放redis内存资源
		err = global.GVA_REDIS.Del(context.Background(), filename).Err()
		if err != nil {
			global.GVA_LOG.Error("Error:" + err.Error())
			continue
		}
		// 设置1/5秒的时间间隔，可以显著降低cpu使用率
		time.Sleep(time.Second / 5)
	}
}
