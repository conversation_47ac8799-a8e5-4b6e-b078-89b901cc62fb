package config

type Server struct {
	JWT     JWT     `mapstructure:"jwt" json:"jwt" yaml:"jwt"`
	Zap     Zap     `mapstructure:"zap" json:"zap" yaml:"zap"`
	Redis   Redis   `mapstructure:"redis" json:"redis" yaml:"redis"`
	Email   Email   `mapstructure:"email" json:"email" yaml:"email"`
	System  System  `mapstructure:"system" json:"system" yaml:"system"`
	Captcha Captcha `mapstructure:"captcha" json:"captcha" yaml:"captcha"`
	// auto
	AutoCode Autocode `mapstructure:"autocode" json:"autocode" yaml:"autocode"`
	// gorm
	Mysql  Mysql           `mapstructure:"mysql" json:"mysql" yaml:"mysql"`
	Mssql  Mssql           `mapstructure:"mssql" json:"mssql" yaml:"mssql"`
	Pgsql  Pgsql           `mapstructure:"pgsql" json:"pgsql" yaml:"pgsql"`
	Oracle Oracle          `mapstructure:"oracle" json:"oracle" yaml:"oracle"`
	DBList []SpecializedDB `mapstructure:"db-list" json:"db-list" yaml:"db-list"`
	// oss
	Local      Local      `mapstructure:"local" json:"local" yaml:"local"`
	Qiniu      Qiniu      `mapstructure:"qiniu" json:"qiniu" yaml:"qiniu"`
	AliyunOSS  AliyunOSS  `mapstructure:"aliyun-oss" json:"aliyun-oss" yaml:"aliyun-oss"`
	HuaWeiObs  HuaWeiObs  `mapstructure:"hua-wei-obs" json:"hua-wei-obs" yaml:"hua-wei-obs"`
	TencentCOS TencentCOS `mapstructure:"tencent-cos" json:"tencent-cos" yaml:"tencent-cos"`
	AwsS3      AwsS3      `mapstructure:"aws-s3" json:"aws-s3" yaml:"aws-s3"`

	Excel Excel `mapstructure:"excel" json:"excel" yaml:"excel"`
	Timer Timer `mapstructure:"timer" json:"timer" yaml:"timer"`

	// 跨域配置
	Cors CORS `mapstructure:"cors" json:"cors" yaml:"cors"`

	// 镜像配置
	Image Image `mapstructure:"image" json:"image" yaml:"image"`

	// ELK日志配置
	ELK ELK `mapstructure:"elk" json:"elk" yaml:"elk"`

	Vim Vim `mapstructure:"vim" json:"vim" yaml:"vim"`

	// ntp服务器设置
	NTP NTP `mapstructure:"ntp" json:"ntp" yaml:"ntp"`

	// 个性化配置
	Honeynet Honeynet `mapstructure:"honeynet" json:"honeynet" yaml:"honeynet"`

	// 报警设置
	AlarmSetting AlarmSetting `mapstructure:"alarm-setting" json:"alarm-setting" yaml:"alarm-setting"`

	//备份
	Backup Backup `mapstructure:"backup" json:"backup" yaml:"backup"`

	// 固件分析配置
	FirmwareAnalysis FirmwareAnalysis `mapstructure:"firmware-analysis" json:"firmware-analysis" yaml:"firmware-analysis"`
}
