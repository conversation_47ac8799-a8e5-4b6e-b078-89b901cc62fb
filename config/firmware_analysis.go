package config

// FirmwareAnalysis 固件分析配置
type FirmwareAnalysis struct {
	// Syslog服务器配置
	SyslogServer SyslogServer `mapstructure:"syslog-server" json:"syslog-server" yaml:"syslog-server"`
	
	// 分片传输配置
	ChunkTransfer ChunkTransfer `mapstructure:"chunk-transfer" json:"chunk-transfer" yaml:"chunk-transfer"`
}

// SyslogServer Syslog服务器配置
type SyslogServer struct {
	// 目标地址，格式: IP:端口
	TargetAddr string `mapstructure:"target-addr" json:"target-addr" yaml:"target-addr"`
	
	// 连接超时时间（秒）
	ConnectTimeout int `mapstructure:"connect-timeout" json:"connect-timeout" yaml:"connect-timeout"`
	
	// 写入超时时间（秒）
	WriteTimeout int `mapstructure:"write-timeout" json:"write-timeout" yaml:"write-timeout"`
}

// ChunkTransfer 分片传输配置
type ChunkTransfer struct {
	// 最大分片大小（字节）
	MaxChunkSize int `mapstructure:"max-chunk-size" json:"max-chunk-size" yaml:"max-chunk-size"`
	
	// 分片间延迟（毫秒）
	ChunkDelay int `mapstructure:"chunk-delay" json:"chunk-delay" yaml:"chunk-delay"`
	
	// 直接发送阈值（字节），小于此值直接发送，不分片
	DirectSendThreshold int `mapstructure:"direct-send-threshold" json:"direct-send-threshold" yaml:"direct-send-threshold"`
	
	// 分片超时时间（分钟）
	ChunkTimeout int `mapstructure:"chunk-timeout" json:"chunk-timeout" yaml:"chunk-timeout"`
}
